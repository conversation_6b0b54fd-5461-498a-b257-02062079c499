---
// Define types for better type safety
type ThrobberType =
  | "dots"
  | "spin"
  | "pulse"
  | "bar"
  | "bounce"
  | "grid"
  | "ring"
  | "orbit"
  | "wave"
  | "ripple"
  | "clock"
  | "infinity"
  | "braille";
type ThrobberSize = "xs" | "sm" | "md" | "lg";
type ThrobberSpeed = "slow" | "normal" | "fast";

interface Props {
  type?: ThrobberType | undefined;
  size?: ThrobberSize | undefined;
  color?: string | undefined;
  text?: string | undefined;
  className?: string | undefined;
  speed?: ThrobberSpeed | undefined;
  center?: boolean | undefined;
  dotCount?: number | undefined;
}

const {
  type = "braille",
  size = "md",
  color = "#8fbcbb", // Default to your accent color
  text = "",
  className = "",
  speed = "normal",
  center = false,
  dotCount = 3,
} = Astro.props as Props;

// Size mapping in rem units - smaller steps for CLI look
const sizeMap: Record<ThrobberSize, string> = {
  xs: "0.5rem",
  sm: "0.75rem",
  md: "1rem",
  lg: "1.25rem",
};

// Speed mapping in seconds
const speedMap: Record<ThrobberSpeed, string> = {
  slow: "1.5s",
  normal: "0.8s",
  fast: "0.4s",
};

const sizePx = sizeMap[size];
const animationSpeed = speedMap[speed];
const containerClass = `inline-flex items-center gap-1.5 font-mono text-xs ${className} ${
  center ? "justify-center w-full" : ""
}`;

// Inline styles for the container
const containerStyle = {
  "--throbber-color": color,
  "--throbber-size": sizePx,
  "--throbber-speed": animationSpeed,
  color: color,
  fontSize: sizePx,
  lineHeight: "1",
};

// Convert style object to string for the style attribute
const containerStyleString = Object.entries(
  containerStyle as Record<string, string>
)
  .map(([key, value]) => `${key}: ${value}`)
  .join(";");

// Generate dots for various throbber types
const dots = Array.from({ length: Math.max(2, Math.min(6, dotCount)) });

// CLI style base classes
const cliBase = "inline-block bg-current rounded-sm";
const cliDot = `${cliBase} w-1 h-1`;
const cliBar = `${cliBase} h-1`;
const cliBorder = "border-current";
---

<div class={containerClass} style={containerStyleString}>
  <div
    class={`throbber throbber-${type} inline-flex items-center justify-center relative`}
  >
    {/* Dots */}
    {
      type === "dots" && (
        <span class="flex items-center gap-0.5">
          {dots.map((_, i) => (
            <span
              class={`${cliDot} animate-pulse`}
              style={`animation-delay: ${i * 0.15}s;`}
              data-index={i}
            />
          ))}
        </span>
      )
    }

    {/* Spin */}
    {
      type === "spin" && (
        <span class="relative w-3 h-3">
          <span
            class={`absolute inset-0 border ${cliBorder} border-t-transparent rounded-full animate-spin`}
          />
        </span>
      )
    }

    {/* Pulse */}
    {type === "pulse" && <span class={`${cliDot} animate-pulse`} />}

    {/* Bar */}
    {
      type === "bar" && (
        <span class="relative w-8 h-1 bg-current/10 rounded-sm overflow-hidden">
          <span
            class={`absolute inset-y-0 left-0 w-1/3 h-full ${cliBase} animate-slide`}
          />
        </span>
      )
    }

    {/* Bounce */}
    {
      type === "bounce" && (
        <span class="relative w-3 h-3">
          <span class={`${cliDot} animate-bounce`} />
        </span>
      )
    }

    {/* Grid */}
    {
      type === "grid" && (
        <div class="grid grid-cols-3 gap-0.5 w-4 h-4">
          {Array.from({ length: 9 }).map((_, i) => (
            <span
              class={`${cliDot} animate-pulse`}
              style={`animation-delay: ${i * 0.03 + Math.floor(i / 3) * 0.03}s;`}
              data-index={i}
            />
          ))}
        </div>
      )
    }

    {/* Ring */}
    {
      type === "ring" && (
        <span class="relative w-3 h-3">
          <span
            class={`absolute inset-0 border ${cliBorder} border-t-transparent rounded-full animate-spin`}
          />
        </span>
      )
    }

    {/* Orbit */}
    {
      type === "orbit" && (
        <span class="relative w-3 h-3">
          {dots.map((_, i) => (
            <span
              class={`${cliDot} absolute opacity-0 animate-orbit`}
              style={`
              --start-angle: ${(i / dots.length) * 360}deg;
              animation-delay: ${i * (0.5 / dots.length)}s;
              transform: rotate(${(i / dots.length) * 360}deg) translateX(0.5rem) rotate(-${(i / dots.length) * 360}deg);
            `}
              data-index={i}
            />
          ))}
        </span>
      )
    }

    {/* Wave */}
    {
      type === "wave" && (
        <span class="flex items-end justify-center gap-0.5 h-3">
          {dots.map((_, i) => (
            <span
              class={`${cliBase} w-0.5 animate-wave`}
              style={`
              height: ${40 + Math.sin(i * 0.8) * 30}%;
              animation-delay: ${i * 0.1}s;
            `}
              data-index={i}
            />
          ))}
        </span>
      )
    }

    {/* Ripple */}
    {
      type === "ripple" && (
        <span class="relative w-3 h-3">
          <span
            class={`absolute inset-0 border ${cliBorder} rounded-full opacity-0 animate-ripple`}
          />
          <span
            class={`absolute inset-0 border ${cliBorder} rounded-full opacity-0 animate-ripple`}
            style="animation-delay: 0.2s"
          />
          <span
            class={`absolute inset-0 border ${cliBorder} rounded-full opacity-0 animate-ripple`}
            style="animation-delay: 0.4s"
          />
        </span>
      )
    }

    {/* Clock */}
    {
      type === "clock" && (
        <span class="relative w-3 h-3">
          <span
            class={`absolute top-1/2 left-1/2 w-1/2 h-0.5 ${cliBase} origin-left animate-clock-hours`}
          />
          <span
            class={`absolute top-1/2 left-1/2 w-1/3 h-0.5 ${cliBase} origin-left animate-clock-minutes`}
          />
          <span class="absolute top-1/2 left-1/2 w-1/2 h-0.5">
            <span
              class={`absolute top-0 left-0 w-0.5 h-0.5 -mt-0.25 -ml-0.25 ${cliBase}`}
            />
          </span>
        </span>
      )
    }

    {/* Infinity */}
    {
      type === "infinity" && (
        <span class="relative w-4 h-2">
          <span
            class={`absolute top-0 left-0 w-2 h-2 border-t-2 border-r-2 ${cliBorder} -rotate-45`}
          />
          <span
            class={`absolute top-0 right-0 w-2 h-2 border-b-2 border-r-2 ${cliBorder} rotate-45`}
          />
          <span class="absolute top-0 left-1/2 w-0.5 h-0.5 -ml-0.25 animate-ping" />
        </span>
      )
    }

    {/* Braille */}
    {
      type === "braille" && (
        <div class="grid grid-rows-2 grid-cols-3 gap-px w-4 h-2.5">
          {Array.from({ length: 6 }).map((_, i) => (
            <span
              class={`${cliDot} animate-pulse`}
              style={`animation-delay: ${i * 0.1}s;`}
              data-index={i}
            />
          ))}
        </div>
      )
    }
  </div>

  {text && <span class="ml-1.5 text-[0.7em] font-mono">{text}</span>}

  <style>
    /* Base styles */
    .throbber {
      font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
        "Liberation Mono", "Courier New", monospace;
    }

    /* Keyframe Animations */
    @keyframes slide {
      0% {
        transform: translateX(-100%);
        opacity: 0.6;
      }
      60% {
        opacity: 1;
      }
      100% {
        transform: translateX(200%);
        opacity: 0.6;
      }
    }

    @keyframes fade-in {
      0%,
      100% {
        opacity: 0.2;
      }
      50% {
        opacity: 1;
      }
    }

    @keyframes orbit {
      0% {
        opacity: 0;
        transform: rotate(0deg) translateX(0.5rem) rotate(0deg);
      }
      50% {
        opacity: 1;
      }
      100% {
        opacity: 0;
        transform: rotate(360deg) translateX(0.5rem) rotate(-360deg);
      }
    }

    @keyframes wave {
      0%,
      100% {
        transform: scaleY(0.5);
        opacity: 0.6;
      }
      50% {
        transform: scaleY(1.2);
        opacity: 1;
      }
    }

    @keyframes ripple {
      0% {
        transform: scale(0.1);
        opacity: 0;
      }
      50% {
        opacity: 0.6;
      }
      100% {
        transform: scale(1);
        opacity: 0;
      }
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    @keyframes bounce {
      0%,
      100% {
        transform: translateY(-50%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
      }
      50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
      }
    }

    @keyframes clock-hours {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    @keyframes clock-minutes {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(4320deg);
      }
    }

    /* Animation Classes */
    .animate-slide {
      animation: slide var(--throbber-speed, 1s) ease-in-out infinite;
    }

    .animate-fade-in {
      animation: fade-in 1s ease-in-out infinite;
    }

    .animate-spin {
      animation: spin 1s linear infinite;
    }

    .animate-orbit {
      animation: orbit 2s ease-in-out infinite;
    }

    .animate-wave {
      animation: wave 0.8s ease-in-out infinite;
    }

    .animate-ripple {
      animation: ripple 1.2s ease-out infinite;
    }

    .animate-bounce {
      animation: bounce 0.8s infinite;
    }

    .animate-pulse {
      animation: fade-in 1s ease-in-out infinite;
    }

    .animate-clock-hours {
      animation: clock-hours 12s linear infinite;
    }

    .animate-clock-minutes {
      animation: clock-minutes 12s linear infinite;
    }
  </style>
</div>
